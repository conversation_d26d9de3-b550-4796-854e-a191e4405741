"use client";
import { AppSidebar, NavItem } from "@/components/custom/app-sidebar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { LayoutDashboard, ScanEye } from "lucide-react";
import Header from "./components/header";
// Menu items.
const items: NavItem[] = [
	{
		title: "Dashboard",
		url: "/dashboard",
		icon: LayoutDashboard,
	},
	{
		title: "Guidance Templates",
		url: "/guidance-templates",
		icon: Scan<PERSON>ye,
	},
	{
		title: "Premises",
		url: "/premises",
		icon: Scan<PERSON>ye,
	},
  {
		title: "Incidents",
		url: "/incidents",
		icon: <PERSON>an<PERSON><PERSON>,
	},
   {
		title: "Cameras",
		url: "/cameras",
		icon: <PERSON>an<PERSON><PERSON>,
	},
  {
		title: "Guards",
		url: "/guards",
		icon: <PERSON>an<PERSON><PERSON>,
	},
    {
		title: "Settings",
		url: "/settings",
		icon: ScanEye,
	},
];
function OperatorLayout({ children }: { children: React.ReactNode }) {
	return (
		<>
			<SidebarProvider>
				<div className="w-fit relative">
					<AppSidebar items={items} />
					<SidebarTrigger className="top-0 right-[-25px] absolute " />
				</div>
				<div className="flex flex-col w-full">
					  <Header />
					{children}
				</div>
			</SidebarProvider>
		</>
	);
}

export default OperatorLayout;
