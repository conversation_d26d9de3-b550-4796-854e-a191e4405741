'use client'
import React, { useRef, useState, useEffect } from 'react';

// Define interfaces for signaling messages
interface SignalingMessage {
  type: 'sdpOffer' | 'sdpAnswer' | 'iceCandidate' | 'error' | 'viewerOffer';
  payload: any; // Payload will be the SDP object or ICE candidate object
  cameraId?: string; // Identifier for the stream, optional for some messages
}

// Main App component for WebRTC Publisher functionality
export default function App(): JSX.Element {
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const remoteStreamRef = useRef<MediaStream | null>(null);

  // Use useRef for peerConnection to avoid stale closures in event handlers
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  // Ref to store ICE candidates received before remote description is set
  const iceCandidateQueueRef = useRef<RTCIceCandidate[]>([]);

  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [cameraId, setCameraId] = useState<string>('');
  const [joinCameraId, setJoinCameraId] = useState<string>('');
  const [signalingStatus, setSignalingStatus] = useState<string>('Idle');
  const [isLocalStreamActive, setIsLocalStreamActive] = useState<boolean>(false);
  const [isPublishing, setIsPublishing] = useState<boolean>(false);
  const [isViewing, setIsViewing] = useState<boolean>(false);
  const [isRemoteStreamActive, setIsRemoteStreamActive] = useState<boolean>(false);
  const [mode, setMode] = useState<'publish' | 'view'>('publish');

  const iceServers: RTCIceServer[] = [
    { urls: 'stun:stun.l.google.com:19302' },
  ];
  // Function to process queued ICE candidates
  const processQueuedIceCandidates = async (pc: RTCPeerConnection) => {
    if (pc && pc.remoteDescription && iceCandidateQueueRef.current.length > 0) {
      console.log(`Processing ${iceCandidateQueueRef.current.length} queued ICE candidates...`);
      for (const candidate of iceCandidateQueueRef.current) {
        try {
          await pc.addIceCandidate(candidate);
          console.log('Added queued ICE candidate:', candidate.toJSON());
        } catch (e) {
          console.error('Error adding queued ICE candidate:', e, 'Candidate:', candidate.toJSON());
        }
      }
      iceCandidateQueueRef.current = []; // Clear the queue
      setSignalingStatus('Queued ICE Candidates added.');
    }
  };


  // --- WebSocket Connection Management ---
  useEffect(() => {
    setSignalingStatus('Connecting to signaling server...');
    const ws = new WebSocket('ws://localhost:1325/ws'); // Adjust URL if your backend is different

    ws.onopen = () => {
      console.log('WebSocket connected!');
      setSignalingStatus('Connected to signaling server.');
    };

    // The onmessage handler now uses peerConnectionRef.current
    ws.onmessage = async (event: MessageEvent) => {
      try {
        const msg: SignalingMessage = JSON.parse(event.data);
        console.log('Received message:', msg);
        // Access the current peer connection from the ref
        const currentPC = peerConnectionRef.current;
        // Re-enabled camera ID check for safety
        // if (msg.cameraId && msg.cameraId !== cameraId && msg.cameraId !== joinCameraId) {
        //     console.warn('Received message for a different camera ID, ignoring:', msg.cameraId);
        //     return;
        // }

        switch (msg.type) {
          case 'sdpAnswer':
            // Handle SDP Answer for both publisher and viewer
            if (currentPC && msg.payload) {
              console.log('Setting remote description (answer):', msg.payload);
              const remoteSdp = new RTCSessionDescription({
                sdp: atob(msg.payload), // Decode Base64 SDP received from backend
                type: 'answer',
              });
              await currentPC.setRemoteDescription(remoteSdp);

              if (isPublishing) {
                setSignalingStatus('Publisher: SDP Answer received. Stream is live!');
              } else if (isViewing) {
                setSignalingStatus('Viewer: SDP Answer received. Connecting to stream...');
              }

              // After setting remote description, process any queued ICE candidates
              processQueuedIceCandidates(currentPC);
            } else {
              console.error('Invalid SDP Answer payload or PeerConnection not initialized:', msg.payload);
            }
            break;
          case 'viewerOffer': {
            if (currentPC && msg.payload) {
              console.log('Received viewer offer:', msg.payload);
              const remoteSdp = new RTCSessionDescription({
                sdp: atob(msg.payload.sdp), // Decode Base64 SDP received from backend
                type: msg.payload.type,
              });
              await currentPC.setRemoteDescription(remoteSdp);

              // Process any queued ICE candidates after setting remote description
              processQueuedIceCandidates(currentPC);

              setSignalingStatus('Viewer: Offer received. Sending answer...');
              // Create and send an SDP answer back to the server
              const answer = await currentPC.createAnswer();
              await currentPC.setLocalDescription(answer);
              websocket?.send(JSON.stringify({
                type: 'sdpAnswer',
                payload: {
                  sdp: btoa(answer.sdp || ''), // Convert SDP string to Base64
                  type: answer.type,
                },
                cameraId: msg.cameraId, // Use the camera ID from the offer
              } as SignalingMessage));
            }
            break;
          }
          case 'iceCandidate':
            if (currentPC && msg.payload) {
              console.log('Attempting to add ICE candidate. Received raw payload:', msg.payload); // Log the raw payload

              const candidateInit: RTCIceCandidateInit = {
                candidate: msg.payload.candidate,
                sdpMid: msg.payload.sdpMid,
                sdpMLineIndex: msg.payload.sdpMLineIndex,
              };

              try {
                // Check if remote description is already set
                if (currentPC.remoteDescription) {
                  await currentPC.addIceCandidate(new RTCIceCandidate(candidateInit));
                  setSignalingStatus('ICE Candidate added.');
                } else {
                  // If remote description is not set yet, queue the candidate
                  console.log('Queueing ICE candidate (remote description not set yet):', candidateInit);
                  iceCandidateQueueRef.current.push(new RTCIceCandidate(candidateInit));
                  setSignalingStatus('ICE Candidate queued.');
                }
              } catch (e) {
                console.error(
                  'Error adding ICE candidate (likely malformed or state issue): ', e,
                  'Problematic object:', candidateInit
                );
              }
            } else {
              console.warn('Received ICE candidate without payload or PeerConnection not ready.');
            }
            break;
          case 'error':
            console.error('Server error:', msg.payload?.message || 'Unknown error');
            setSignalingStatus(`Error from server: ${msg.payload?.message || 'Unknown error'}`);
            stopPublishing(); // Attempt to clean up
            break;
          default:
            console.warn('Unknown signaling message type:', msg.type);
        }
      } catch (error) {
        console.error('Error parsing or handling WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected!');
      setSignalingStatus('Disconnected from signaling server.');
      setWebsocket(null);
      // Attempt to clean up WebRTC connection if WS closes unexpectedly
      if (peerConnectionRef.current) {
        peerConnectionRef.current.close();
        peerConnectionRef.current = null; // Clear the ref
      }
      iceCandidateQueueRef.current = []; // Clear queue on disconnect
      setIsPublishing(false); // Stop publishing state
    };

    ws.onerror = (error: Event) => {
      console.error('WebSocket error:', error);
      setSignalingStatus('WebSocket error.');
    };

    setWebsocket(ws);

    // Cleanup function for useEffect
    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, []); // Empty dependency array: runs only once on mount

  // --- WebRTC Setup and Signaling Logic ---

  const getLocalStream = async (): Promise<MediaStream | null> => {
    try {
      const stream: MediaStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });

      // Debug: Log what tracks we actually got
      console.log('Local stream tracks:', stream.getTracks().map(t => ({
        kind: t.kind,
        enabled: t.enabled,
        readyState: t.readyState,
        id: t.id,
        label: t.label
      })));

      const videoTracks = stream.getVideoTracks();
      const audioTracks = stream.getAudioTracks();
      console.log('Local video tracks:', videoTracks.length);
      console.log('Local audio tracks:', audioTracks.length);

      if (videoTracks.length > 0) {
        const videoTrack = videoTracks[0];
        console.log('Local video track settings:', videoTrack.getSettings ? videoTrack.getSettings() : 'getSettings not available');
      }

      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
      setIsLocalStreamActive(true);
      return stream;
    } catch (error: any) {
      console.error('Error accessing media devices:', error);
      setSignalingStatus(`Error: ${error.message}`);
      return null;
    }
  };

  const startPublishing = async (): Promise<void> => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      setSignalingStatus('WebSocket not connected. Please wait or refresh.');
      return;
    }
    if (!cameraId) {
      setSignalingStatus('Please enter a Camera ID.');
      return;
    }

    setIsPublishing(true);
    setSignalingStatus('Starting publisher WebRTC...');

    const localStream = await getLocalStream();
    if (!localStream) {
      setIsPublishing(false);
      return;
    }

    const pc: RTCPeerConnection = new RTCPeerConnection({ iceServers });
    peerConnectionRef.current = pc; // Assign the new PeerConnection to the ref

    // Add tracks to peer connection with debugging
    localStream.getTracks().forEach((track: MediaStreamTrack) => {
      console.log('Adding track to publisher PC:', {
        kind: track.kind,
        enabled: track.enabled,
        readyState: track.readyState,
        id: track.id
      });
      pc.addTrack(track, localStream);
    });

    // Verify what senders we have
    console.log('Publisher PC senders:', pc.getSenders().map(sender => ({
      track: sender.track ? {
        kind: sender.track.kind,
        enabled: sender.track.enabled,
        readyState: sender.track.readyState
      } : null
    })));

    // pc.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
    //   if (event.candidate && websocket && websocket.readyState === WebSocket.OPEN) {
    //     console.log('Sending ICE candidate:', event.candidate.toJSON());
    //     // Send ICE candidate over WebSocket
    //     websocket.send(JSON.stringify({
    //       type: 'iceCandidate',
    //       payload: event.candidate.toJSON(), // Convert RTCIceCandidate to JSON serializable object
    //       cameraId: cameraId,
    //     } as SignalingMessage));
    //   }
    // };

    pc.onconnectionstatechange = () => {
      console.log('Publisher PeerConnection state:', pc.connectionState);
      setSignalingStatus(`Publisher: ${pc.connectionState}`);
      if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed' || pc.connectionState === 'closed') {
        console.log('Publisher connection closed or failed, cleaning up...');
        stopPublishing(); // Call stopPublishing to clean up
      }
    };

    try {
      const offer: RTCSessionDescriptionInit = await pc.createOffer();
      await pc.setLocalDescription(offer);

      console.log('Sending SDP offer:', offer);
      // Send SDP offer over WebSocket
      websocket.send(
        JSON.stringify({
          type: 'sdpOffer',
          payload: {
            sdp: btoa(offer.sdp || ''), // Convert SDP string to Base64
            type: offer.type,
          },
          cameraId: cameraId, // Use the state variable
        } as SignalingMessage)
      );

      setSignalingStatus('SDP Offer sent, waiting for answer...');

    } catch (error: any) {
      console.error('Error starting publishing:', error);
      setSignalingStatus(`Publisher Error: ${error.message}`);
      stopPublishing(); // Call stopPublishing to clean up
    }
  };

  const stopPublishing = () => {
    // Check ref.current before closing
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null; // Clear the ref
    }
    if (localVideoRef.current && localVideoRef.current.srcObject) {
      (localVideoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      localVideoRef.current.srcObject = null;
    }
    setSignalingStatus('Idle');
    setIsLocalStreamActive(false);
    setIsPublishing(false);
    iceCandidateQueueRef.current = []; // Clear queue on stop
  };

  const joinStream = async (): Promise<void> => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      setSignalingStatus('WebSocket not connected. Please wait or refresh.');
      return;
    }
    if (!joinCameraId) {
      setSignalingStatus('Please enter a Camera ID to join.');
      return;
    }

    setIsViewing(true);
    setSignalingStatus('Joining stream...');

    const pc: RTCPeerConnection = new RTCPeerConnection({ iceServers });
    peerConnectionRef.current = pc;

    // Add transceivers to indicate we want to receive audio and video
    pc.addTransceiver('video', { direction: 'recvonly' });

    // Set up to receive remote stream
    pc.ontrack = (event: RTCTrackEvent) => {
      console.log('Received remote track:', event);
      console.log('Track kind:', event.track.kind);
      console.log('Track readyState:', event.track.readyState);
      console.log('Streams:', event.streams);

      // Create or get existing remote stream
      let remoteStream = remoteStreamRef.current;
      if (!remoteStream) {
        remoteStream = new MediaStream();
        remoteStreamRef.current = remoteStream;
      }

      // Add the track to our remote stream
      remoteStream.addTrack(event.track);

      console.log('Updated remote stream tracks:', remoteStream.getTracks().map(t => ({
        kind: t.kind,
        enabled: t.enabled,
        readyState: t.readyState,
        id: t.id,
        label: t.label,
        muted: t.muted
      })));

      // Check specifically for video tracks
      const videoTracks = remoteStream.getVideoTracks();
      const audioTracks = remoteStream.getAudioTracks();
      console.log('Video tracks count:', videoTracks.length);

      if (videoTracks.length > 0) {
        const videoTrack = videoTracks[0];
        const settings = videoTrack.getSettings ? videoTrack.getSettings() : {};
        console.log('Video track details:', {
          enabled: videoTrack.enabled,
          muted: videoTrack.muted,
          readyState: videoTrack.readyState,
          settings: settings
        });

        // Check if video track has actual dimensions
        if (settings.width && settings.height) {
          console.log(`Video track dimensions: ${settings.width}x${settings.height}`);
        } else {
          console.warn('Video track has no dimensions in settings!');
        }

        // Try to get capabilities
        if (videoTrack.getCapabilities) {
          console.log('Video track capabilities:', videoTrack.getCapabilities());
        }
      }

      // Set the stream to video element
      if (remoteVideoRef.current && remoteVideoRef.current.srcObject !== remoteStream) {
        console.log('Setting remote stream to video element');
        remoteVideoRef.current.srcObject = remoteStream;
        setIsRemoteStreamActive(true);
        setSignalingStatus('Connected to remote stream!');

        // Force video to play
        remoteVideoRef.current.play().catch(e => {
          console.error('Error playing remote video:', e);
        });

        // Check video dimensions after a delay
        setTimeout(() => {
          if (remoteVideoRef.current) {
            console.log('Video element dimensions after delay:', {
              videoWidth: remoteVideoRef.current.videoWidth,
              videoHeight: remoteVideoRef.current.videoHeight,
              clientWidth: remoteVideoRef.current.clientWidth,
              clientHeight: remoteVideoRef.current.clientHeight,
              readyState: remoteVideoRef.current.readyState,
              networkState: remoteVideoRef.current.networkState
            });

            // Try to force a repaint
            if (remoteVideoRef.current.videoWidth === 0) {
              console.log('Trying to force video refresh...');
              remoteVideoRef.current.load();
              remoteVideoRef.current.play().catch(console.error);
            }
          }
        }, 2000);
      }
    };

    // pc.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
    //   if (event.candidate && websocket && websocket.readyState === WebSocket.OPEN) {
    //     console.log('Sending ICE candidate:', event.candidate.toJSON());
    //     websocket.send(JSON.stringify({
    //       type: 'iceCandidate',
    //       payload: event.candidate.toJSON(),
    //       cameraId: joinCameraId,
    //       viewerId:'123'
    //     } as SignalingMessage));
    //   }
    // };

    pc.onconnectionstatechange = () => {
      console.log('Connection state changed:', pc.connectionState);
      if (pc.connectionState === 'connected') {
        setSignalingStatus('Successfully connected to stream!');
      } else if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {
        setSignalingStatus('Connection lost or failed.');
        stopViewing();
      }
    };

    try {
      // Create offer as viewer
      const offer: RTCSessionDescriptionInit = await pc.createOffer();
      await pc.setLocalDescription(offer);

      console.log('Sending viewer SDP offer:', offer);

      // Send join stream request with offer
      websocket.send(JSON.stringify({
        type: 'viewerOffer',
        payload: {
          sdp: btoa(offer.sdp || ''), // Convert SDP string to Base64
          type: offer.type,
          viewerId: '123'
        },
        cameraId: joinCameraId,
      } as SignalingMessage));

      setSignalingStatus('Join request with offer sent, waiting for answer...');
    } catch (error: any) {
      console.error('Error joining stream:', error);
      setSignalingStatus(`Join Error: ${error.message}`);
      stopViewing();
    }
  };

  const stopViewing = () => {
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    if (remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = null;
    }
    if (remoteStreamRef.current) {
      remoteStreamRef.current.getTracks().forEach(track => track.stop());
      remoteStreamRef.current = null;
    }
    setSignalingStatus('Idle');
    setIsRemoteStreamActive(false);
    setIsViewing(false);
    iceCandidateQueueRef.current = [];
  };

  // Cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      stopPublishing(); // Ensure everything is cleaned up on unmount
      stopViewing(); // Also cleanup viewing
    };
  }, []); // Only runs on mount and unmount


  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4">
      <script src="https://cdn.tailwindcss.com"></script>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />

      <style jsx global>{`
        body {
          font-family: 'Inter', sans-serif;
        }
      `}</style>

      <h1 className="text-4xl font-bold mb-8 text-gray-800 rounded-lg p-2 bg-white shadow-md">
        🎥 WebRTC Live Stream Platform
      </h1>

      {/* Mode Selector */}
      <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-3xl mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-gray-700 text-center">Choose Mode</h2>
        <div className="flex gap-4 justify-center">
          <button
            onClick={() => {
              setMode('publish');
              stopViewing();
              stopPublishing();
            }}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              mode === 'publish'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📡 Publish Stream
          </button>
          <button
            onClick={() => {
              setMode('view');
              stopPublishing();
              stopViewing();
            }}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              mode === 'view'
                ? 'bg-green-600 text-white shadow-lg'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            👁️ Join Stream
          </button>
        </div>
      </div>

      <div className="flex flex-col gap-8 w-full max-w-5xl mb-8">
        {mode === 'publish' && (
          /* Local Video Section (Host's Camera) */
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">Your Local Stream (Publishing)</h2>
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-auto bg-gray-800 rounded-lg shadow-inner border border-gray-300"
            ></video>
            <p className="text-sm text-gray-500 mt-2">
              {isLocalStreamActive ? 'Your camera is active and ready for publishing.' : 'Camera not active.'}
            </p>
          </div>
        )}

        {mode === 'view' && (
          /* Remote Video Section (Viewing Stream) */
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-2xl font-semibold mb-4 text-gray-700">Remote Stream (Viewing)</h2>
            <div className="space-y-4">
              <video
                ref={remoteVideoRef}
                autoPlay
                playsInline
                controls
                muted={false}
                style={{ width: '100%', height: 'auto', minHeight: '200px' }}
                className="bg-gray-800 rounded-lg shadow-inner border border-gray-300"
                onLoadedMetadata={() => {
                  console.log('Remote video metadata loaded');
                  console.log('Video dimensions:', remoteVideoRef.current?.videoWidth, 'x', remoteVideoRef.current?.videoHeight);
                }}
                onCanPlay={() => {
                  console.log('Remote video can play');
                }}
                onPlay={() => {
                  console.log('Remote video started playing');
                }}
                onError={(e) => {
                  console.error('Remote video error:', e);
                }}
                onResize={() => {
                  console.log('Video resized:', remoteVideoRef.current?.videoWidth, 'x', remoteVideoRef.current?.videoHeight);
                }}
              ></video>

              {/* Debug info */}
              <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded">
                <div>Video Element Debug:</div>
                <div>Ready State: {remoteVideoRef.current?.readyState || 'N/A'}</div>
                <div>Network State: {remoteVideoRef.current?.networkState || 'N/A'}</div>
                <div>Video Size: {remoteVideoRef.current?.videoWidth || 0} x {remoteVideoRef.current?.videoHeight || 0}</div>
                <div>Element Size: {remoteVideoRef.current?.clientWidth || 0} x {remoteVideoRef.current?.clientHeight || 0}</div>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              {isRemoteStreamActive ? 'Connected to remote stream.' : 'No remote stream connected.'}
            </p>
          </div>
        )}
      </div>

      {/* Control Panel */}
      <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-3xl flex flex-col items-center gap-4">
        <h2 className="text-2xl font-semibold text-gray-700 mb-4">
          {mode === 'publish' ? 'Publisher Controls' : 'Viewer Controls'}
        </h2>

        {mode === 'publish' && (
          <>
            <div className="w-full mb-4">
              <input
                type="text"
                placeholder="Enter Camera ID to publish (e.g., 'my-live-cam')"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={cameraId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCameraId(e.target.value)}
                disabled={isPublishing || !websocket || websocket.readyState !== WebSocket.OPEN}
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full justify-center">
              <button
                onClick={startPublishing}
                disabled={!cameraId || isPublishing || !websocket || websocket.readyState !== WebSocket.OPEN}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isPublishing ? 'Publishing...' : 'Start Publishing Stream'}
              </button>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full justify-center mt-4">
              <button
                onClick={stopPublishing}
                disabled={!isPublishing}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Stop Publishing
              </button>
            </div>
          </>
        )}

        {mode === 'view' && (
          <>
            <div className="w-full mb-4">
              <input
                type="text"
                placeholder="Enter Camera ID to join (e.g., 'my-live-cam')"
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                value={joinCameraId}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setJoinCameraId(e.target.value)}
                disabled={isViewing || !websocket || websocket.readyState !== WebSocket.OPEN}
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full justify-center">
              <button
                onClick={joinStream}
                disabled={!joinCameraId || isViewing || !websocket || websocket.readyState !== WebSocket.OPEN}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isViewing ? 'Joining...' : 'Join Stream'}
              </button>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 w-full justify-center mt-4">
              <button
                onClick={stopViewing}
                disabled={!isViewing}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Stop Viewing
              </button>
            </div>
          </>
        )}

        <p className="text-lg text-gray-700 mt-4">
          Status: <span className="font-medium text-blue-800">{signalingStatus}</span>
        </p>
      </div>

      <p className="mt-8 text-sm text-gray-600 text-center">
        {mode === 'publish'
          ? 'Enter a Camera ID and click "Start Publishing Stream" to broadcast your camera feed.'
          : 'Enter a Camera ID and click "Join Stream" to view someone else\'s live stream.'
        }
      </p>
    </div>
  );
}
